/* Destination Showcase Styles - Based on reference design */

/* Modern Section Styles */
.section-title {
  position: relative;
  margin-bottom: 2.5rem;
  text-align: center;
}

.section-title h2 {
  font-weight: 700;
  position: relative;
  display: inline-block;
  padding-bottom: 10px;
  font-size: 2.5rem;
  color: #333;
}

.section-title h2:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 50px;
  height: 3px;
  background-color: #4caf50;
  transform: translateX(-50%);
}

/* Modern Card Styles */
.modern-card {
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: none;
  margin-bottom: 30px;
}

.modern-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.modern-card .card-img-top {
  height: 200px;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.modern-card:hover .card-img-top {
  transform: scale(1.05);
}

.modern-card .card-body {
  padding: 1.5rem;
}

.modern-card .card-title {
  font-weight: 600;
  margin-bottom: 0.75rem;
  font-size: 1.2rem;
}

.modern-card .card-text {
  color: #666;
  margin-bottom: 1.25rem;
  font-size: 0.9rem;
}

.modern-card .btn {
  border-radius: 30px;
  padding: 0.5rem 1.5rem;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.modern-card .btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Modern Button Styles */
.btn-modern {
  border-radius: 30px;
  padding: 0.8rem 2rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
}

.btn-modern:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.btn-modern-primary {
  background-color: #4caf50;
  color: white;
}

.btn-modern-primary:hover {
  background-color: #3d8b40;
  color: white;
}

.btn-modern-outline {
  background-color: transparent;
  border: 2px solid #4caf50;
  color: #4caf50;
}

.btn-modern-outline:hover {
  background-color: #4caf50;
  color: white;
}

/* Modern Search Form */
.search-form-modern {
  background-color: white;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-top: -50px;
  position: relative;
  z-index: 10;
}

.search-form-modern .form-control {
  border-radius: 30px;
  padding: 0.75rem 1.25rem;
  border: 1px solid #eee;
  box-shadow: none;
  transition: all 0.3s ease;
}

.search-form-modern .form-control:focus {
  border-color: #4caf50;
  box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25);
}

.search-form-modern .input-group-text {
  border-radius: 30px 0 0 30px;
  background-color: white;
  border: 1px solid #eee;
  border-right: none;
}

.search-form-modern .btn {
  border-radius: 30px;
  padding: 0.75rem 2rem;
  font-weight: 500;
}

/* Main container */
#destination-showcase-container {
  position: relative;
  width: 100%;
  height: 100vh;
  min-height: 600px;
  overflow: hidden;
  margin-top: 0; /* Remove negative margin */
  padding-top: 0; /* Remove extra padding */
}

#destination-showcase-container.full-page {
  height: 100vh;
  width: 100%;
  margin: 0;
  padding: 0;
}

.destination-showcase {
  position: relative;
  width: 100%;
  height: 100vh;
  min-height: 600px;
  overflow: hidden;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}

/* Background overlay for different continents */
.destination-showcase::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    rgba(0, 0, 0, 0.1) 100%
  );
  z-index: 1;
}

/* Background images for different provinces */
.destination-showcase.asia {
  background-image: url("/images/banners/hinh-anh-ben-tre-tho-mong-tru-tinh_022742052.jpg");
}

.destination-showcase.cantho {
  background-image: url("/images/banners/banner_home_01.jpg");
}

.destination-showcase.vinhlong {
  background-image: url("/images/banners/hinh-anh-chieu-hoang-hon-o-ben-tre_022743590.jpg");
}

.destination-showcase.tiengiang {
  background-image: url("/images/banners/banner_home_02.jpg");
}

.destination-showcase.dongthap {
  background-image: url("/images/banners/hinh-anh-trung-tam-thanh-pho-ben-tre-nhin-tu-tren-cao_022746613.jpg");
}

/* Destination container */
.destination-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 2rem;
  z-index: 2;
}

/* Header section with logo and navigation */
.showcase-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 0;
  flex-wrap: wrap;
  position: relative;
  z-index: 10;
}

.showcase-logo {
  font-size: 1.2rem;
  font-weight: 600;
  color: #fff;
  margin-right: 1rem;
}

.showcase-logo a {
  display: inline-block;
  transition: all 0.3s ease;
}

.showcase-logo a:hover {
  opacity: 0.9;
  transform: scale(1.05);
}

.showcase-nav {
  display: flex;
  gap: 1.5rem;
  flex-grow: 1;
  justify-content: center;
}

.showcase-nav-item {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.3s ease;
  padding: 0.5rem 0;
  position: relative;
  font-weight: 500;
}

.showcase-nav-item:hover,
.showcase-nav-item.active {
  color: #fff;
}

.showcase-nav-item::after {
  content: "";
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 50%;
  background-color: #fff;
  transition: width 0.3s ease;
  transform: translateX(-50%);
}

.showcase-nav-item:hover::after,
.showcase-nav-item.active::after {
  width: 30px;
}

.user-actions {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.user-actions .btn {
  transition: all 0.3s ease;
}

.user-actions .btn-outline-light {
  border-color: rgba(255, 255, 255, 0.6);
}

.user-actions .btn-outline-light:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: #fff;
}

.user-actions .btn-success {
  background-color: #4caf50;
  border-color: #4caf50;
}

.user-actions .btn-success:hover {
  background-color: #3d8b40;
  border-color: #3d8b40;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

/* Main content area */
.showcase-content {
  flex: 1;
  display: flex;
  align-items: center;
}

/* Left side with main heading */
.showcase-info {
  width: 40%;
  padding-right: 2rem;
}

.continent-name {
  font-size: 5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #fff;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.continent-description {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2rem;
  max-width: 90%;
  line-height: 1.6;
  text-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
}

.explore-btn {
  display: inline-block;
  padding: 0.8rem 2rem;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 30px;
  font-size: 0.9rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.explore-btn:hover {
  background-color: #3d8b40;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  color: white;
}

/* Right side with destination cards */
.showcase-destinations {
  width: 60%;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 1.5rem;
  height: 450px;
}

.destination-card-large {
  grid-column: 1;
  grid-row: 1 / span 2;
  height: 100%;
}

.destination-card-small {
  height: 100%;
}

/* Destination card styling */
.destination-card {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: all 0.4s ease;
  cursor: pointer;
}

.destination-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.25);
}

.destination-card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.6s ease;
}

.destination-card:hover img {
  transform: scale(1.08);
}

/* Vertical destination card styling */
.vertical-destination-card {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: all 0.4s ease;
  cursor: pointer;
  height: 450px;
  display: block;
  margin-bottom: 20px;
}

.vertical-destination-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.25);
}

.vertical-destination-card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.6s ease;
}

.vertical-destination-card:hover img {
  transform: scale(1.08);
}

.destination-card-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 1.5rem;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.85) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    rgba(0, 0, 0, 0) 100%
  );
  color: white;
  transition: all 0.3s ease;
}

.destination-card:hover .destination-card-overlay {
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.9) 0%,
    rgba(0, 0, 0, 0.5) 50%,
    rgba(0, 0, 0, 0.1) 100%
  );
}

.vertical-destination-card-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 1.5rem;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.85) 0%,
    rgba(0, 0, 0, 0.6) 40%,
    rgba(0, 0, 0, 0.2) 80%,
    rgba(0, 0, 0, 0) 100%
  );
  color: white;
  transition: all 0.3s ease;
  height: 65%; /* Cover more of the card */
}

.vertical-destination-card:hover .vertical-destination-card-overlay {
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.9) 0%,
    rgba(0, 0, 0, 0.7) 40%,
    rgba(0, 0, 0, 0.3) 80%,
    rgba(0, 0, 0, 0.1) 100%
  );
}

.destination-card-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.destination-card-subtitle {
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 0;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Specific styles for vertical cards */
.vertical-destination-card .destination-card-title {
  font-size: 1.8rem;
  margin-bottom: 0.75rem;
}

.vertical-destination-card .destination-card-subtitle {
  font-size: 1rem;
  margin-bottom: 1rem;
}

.vertical-destination-card .card-text {
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.vertical-destination-card .btn {
  padding: 0.6rem 1.5rem;
  font-weight: 500;
}

/* Card action buttons */
.card-actions {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  gap: 0.5rem;
  z-index: 10;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.destination-card:hover .card-actions {
  opacity: 1;
  transform: translateY(0);
}

.card-action-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.card-action-btn:hover {
  background-color: white;
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

/* Navigation controls */
.showcase-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  position: relative;
  z-index: 10;
  margin-top: auto;
}

.continent-nav {
  display: flex;
  gap: 1rem;
  align-items: center;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.nav-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  color: white;
}

.nav-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.continent-indicators {
  display: flex;
  gap: 0.8rem;
}

.continent-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.continent-indicator.active {
  background-color: #fff;
  transform: scale(1.2);
}

/* Tab Navigation Styles */
.showcase-tab-navigation {
  position: absolute;
  top: 2rem;
  right: 2rem;
  z-index: 10;
}

.tab-nav-container {
  display: flex;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 0.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.tab-nav-btn {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: rgba(255, 255, 255, 0.8);
  padding: 0.75rem 1rem;
  border-radius: 10px;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  min-width: 80px;
  font-size: 0.8rem;
  font-weight: 500;
}

.tab-nav-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateY(-2px);
}

.tab-nav-btn.active {
  background: rgba(255, 255, 255, 0.3);
  color: white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.tab-nav-btn i {
  font-size: 1.2rem;
}

/* Tab Content Styles */
.showcase-tab-content {
  position: absolute;
  bottom: 2rem;
  left: 2rem;
  right: 2rem;
  max-height: 60vh;
  overflow-y: auto;
  z-index: 5;
}

.tab-content-panel {
  display: none;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.tab-content-panel.active {
  display: block;
  animation: fadeInUp 0.5s ease-in-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Search Form Styles in Tab */
.search-form-modern {
  background: transparent;
}

.search-tabs {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  margin-bottom: 1rem;
}

.search-tabs .nav-tabs {
  border: none;
  padding: 0.5rem;
}

.search-tabs .nav-link {
  background: transparent;
  border: none;
  color: #666;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
}

.search-tabs .nav-link:hover {
  background: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.search-tabs .nav-link.active {
  background: #4caf50;
  color: white;
}

/* Content Sections */
.destinations-showcase,
.hotels-showcase,
.tours-showcase,
.experiences-showcase,
.about-showcase {
  max-height: 50vh;
  overflow-y: auto;
}

.section-title h2 {
  font-size: 1.8rem;
  margin-bottom: 1rem;
  color: #333;
}

.section-title p {
  color: #666;
  margin-bottom: 1.5rem;
}

/* Card Styles in Tabs */
.destination-card-modern,
.hotel-card-modern,
.tour-card-modern {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  background: white;
  border: none;
}

.destination-card-modern:hover,
.hotel-card-modern:hover,
.tour-card-modern:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.8) 0%,
    rgba(0, 0, 0, 0) 100%
  );
  display: flex;
  align-items: flex-end;
  padding: 1rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.destination-card-modern:hover .card-overlay {
  opacity: 1;
}

.card-content {
  color: white;
}

/* Experience Cards */
.experience-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  height: 100%;
}

.experience-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* AI Recommendation Card */
.ai-recommendation-card {
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  margin-top: 2rem;
}

/* Newsletter Section */
.newsletter-section {
  margin-top: 2rem;
}

.newsletter-card {
  background: rgba(76, 175, 80, 0.1);
  border-radius: 15px;
  border: none;
}

/* Info Cards */
.info-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  height: 100%;
}

.info-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Responsive styles */
@media (max-width: 992px) {
  .showcase-content {
    flex-direction: column;
  }

  .showcase-info,
  .showcase-destinations {
    width: 100%;
  }

  .showcase-info {
    padding-right: 0;
    margin-bottom: 2rem;
  }

  .continent-name {
    font-size: 3.5rem;
  }

  .showcase-tab-navigation {
    position: relative;
    top: auto;
    right: auto;
    margin-bottom: 1rem;
  }

  .tab-nav-container {
    justify-content: center;
    flex-wrap: wrap;
  }

  .tab-nav-btn {
    min-width: 70px;
    padding: 0.5rem 0.75rem;
    font-size: 0.7rem;
  }

  .showcase-tab-content {
    position: relative;
    bottom: auto;
    left: auto;
    right: auto;
    max-height: none;
  }
}

@media (max-width: 768px) {
  .showcase-content {
    padding-top: 2rem;
  }

  .showcase-destinations {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(3, 1fr);
    height: auto;
    gap: 1rem;
  }

  .destination-card-large {
    grid-column: 1;
    grid-row: 1;
    height: 250px;
  }

  .destination-card-small {
    height: 200px;
  }

  .vertical-destination-card {
    height: 400px;
  }

  .vertical-destination-card-overlay {
    height: 70%;
  }

  .continent-name {
    font-size: 3rem;
  }

  .showcase-header {
    flex-wrap: wrap;
    padding: 1rem;
  }

  .showcase-logo {
    flex: 1;
  }

  .user-actions {
    flex: 1;
    justify-content: flex-end;
  }

  .showcase-nav {
    order: 3;
    width: 100%;
    margin-top: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .user-actions .btn-success {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }

  .dropdown-menu {
    position: absolute;
    right: 0;
    left: auto;
  }
}

@media (max-width: 576px) {
  .destination-container {
    padding: 1rem;
  }

  .continent-name {
    font-size: 2.5rem;
  }

  .showcase-nav {
    gap: 0.8rem;
  }

  .showcase-nav-item {
    font-size: 0.8rem;
  }

  .continent-description {
    max-width: 100%;
    font-size: 0.9rem;
  }

  .showcase-controls {
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
  }

  .continent-indicators {
    order: 1;
    width: 100%;
    justify-content: center;
    margin-bottom: 0.5rem;
  }

  .continent-nav {
    order: 2;
  }

  .user-actions {
    flex-wrap: wrap;
    justify-content: flex-end;
    gap: 0.5rem;
  }

  .user-actions .btn-success {
    width: 100%;
    margin-bottom: 0.5rem;
    order: -1;
  }

  .showcase-header {
    padding-bottom: 0;
  }

  .vertical-destination-card {
    height: 350px;
  }

  .vertical-destination-card-overlay {
    height: 75%;
    padding: 1rem;
  }

  .vertical-destination-card .destination-card-title {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
  }

  .vertical-destination-card .card-text {
    margin-bottom: 1rem;
    font-size: 0.85rem;
  }
}
